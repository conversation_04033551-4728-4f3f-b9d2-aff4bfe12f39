import { Heart, Target, Users, Lightbulb } from 'lucide-react';

const AboutMissionMe = () => {
  const features = [
    {
      icon: <Heart className="w-8 h-8 text-blue-600" />,
      title: "Empathetic Guidance",
      description: "We understand that life's challenges require both wisdom and compassion. Every session is tailored to your unique journey."
    },
    {
      icon: <Target className="w-8 h-8 text-blue-600" />,
      title: "Clear Direction",
      description: "Cut through the noise and confusion. We help you identify what truly matters and create actionable steps forward."
    },
    {
      icon: <Users className="w-8 h-8 text-blue-600" />,
      title: "Holistic Approach",
      description: "Whether individual, couples, or family coaching, we address the whole person and all relationships that matter."
    },
    {
      icon: <Lightbulb className="w-8 h-8 text-blue-600" />,
      title: "Practical Tools",
      description: "Gain real-world strategies and emotional intelligence skills that you can apply immediately in your daily life."
    }
  ];

  return (
    <section id="about-missionme" className="section-padding bg-gray-50">
      <div className="container-custom">
        <div className="text-center mb-16">
          <h2 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
            What is MissionMe?
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
            MissionMe is more than life coaching—it's a partnership in your personal transformation. 
            We guide individuals, couples, and families through life's most challenging transitions 
            with empathy, clarity, and practical wisdom.
          </p>
        </div>

        <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8 mb-16">
          {features.map((feature, index) => (
            <div
              key={index}
              className="bg-white p-6 rounded-xl shadow-lg card-hover text-center"
            >
              <div className="flex justify-center mb-4">
                {feature.icon}
              </div>
              <h3 className="text-xl font-semibold text-gray-900 mb-3">
                {feature.title}
              </h3>
              <p className="text-gray-600 leading-relaxed">
                {feature.description}
              </p>
            </div>
          ))}
        </div>

        <div className="bg-white rounded-2xl shadow-xl p-8 md:p-12">
          <div className="grid md:grid-cols-2 gap-12 items-center">
            <div>
              <h3 className="text-3xl font-bold text-gray-900 mb-6">
                How We're Different
              </h3>
              <div className="space-y-4">
                <div className="flex items-start gap-3">
                  <div className="w-2 h-2 bg-blue-600 rounded-full mt-3 flex-shrink-0"></div>
                  <p className="text-gray-700">
                    <strong>Real-world experience:</strong> Nisso brings decades of leadership, 
                    business acumen, and life experience to every conversation.
                  </p>
                </div>
                <div className="flex items-start gap-3">
                  <div className="w-2 h-2 bg-blue-600 rounded-full mt-3 flex-shrink-0"></div>
                  <p className="text-gray-700">
                    <strong>Humor and humanity:</strong> We believe laughter and lightness 
                    are essential ingredients in any transformation journey.
                  </p>
                </div>
                <div className="flex items-start gap-3">
                  <div className="w-2 h-2 bg-blue-600 rounded-full mt-3 flex-shrink-0"></div>
                  <p className="text-gray-700">
                    <strong>Emotional intelligence focus:</strong> We don't just solve problems—we 
                    help you develop the emotional skills to navigate future challenges.
                  </p>
                </div>
                <div className="flex items-start gap-3">
                  <div className="w-2 h-2 bg-blue-600 rounded-full mt-3 flex-shrink-0"></div>
                  <p className="text-gray-700">
                    <strong>Action-oriented:</strong> Every session ends with clear, practical 
                    steps you can take immediately.
                  </p>
                </div>
              </div>
            </div>
            <div className="bg-gradient-to-br from-blue-50 to-blue-100 rounded-xl p-8">
              <blockquote className="text-lg text-gray-700 italic mb-4">
                "Life coaching isn't about having all the answers—it's about asking the right questions 
                and walking alongside you as you discover your own wisdom."
              </blockquote>
              <cite className="text-blue-600 font-semibold">— Nisso Barokas</cite>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default AboutMissionMe;
