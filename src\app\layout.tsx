import type { Metadata } from "next";
import { Inter, Playfair_Display } from "next/font/google";
import "./globals.css";

const inter = Inter({
  variable: "--font-inter",
  subsets: ["latin"],
});

const playfair = Playfair_Display({
  variable: "--font-playfair",
  subsets: ["latin"],
});

export const metadata: Metadata = {
  title: "MissionMe - With You, Every Stroke of the Way",
  description:
    "Life coaching by <PERSON><PERSON>. Inspiring, guiding, and coaching individuals through life transitions, personal development, and emotional intelligence.",
  keywords:
    "life coaching, personal development, emotional intelligence, life transitions, career coaching, relationship coaching",
  authors: [{ name: "<PERSON><PERSON>" }],
  openGraph: {
    title: "MissionMe - With You, Every Stroke of the Way",
    description:
      "Life coaching by <PERSON><PERSON>. Inspiring, guiding, and coaching individuals through life transitions, personal development, and emotional intelligence.",
    type: "website",
  },
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" className="scroll-smooth">
      <body
        className={`${inter.variable} ${playfair.variable} antialiased bg-white text-gray-900`}
      >
        {children}
      </body>
    </html>
  );
}
