import { Shield, Briefcase, Heart, Star } from "lucide-react";

const AboutNisso = () => {
  const credentials = [
    {
      icon: <Shield className="w-6 h-6 text-blue-600" />,
      title: "Former Special Forces",
      description:
        "Leadership under pressure, resilience, and strategic thinking",
    },
    {
      icon: <Briefcase className="w-6 h-6 text-blue-600" />,
      title: "Business Leader",
      description:
        "Decades of executive experience and organizational transformation",
    },
    {
      icon: <Heart className="w-6 h-6 text-blue-600" />,
      title: "Father & Grandfather",
      description:
        "Deep understanding of family dynamics and generational wisdom",
    },
    {
      icon: <Star className="w-6 h-6 text-blue-600" />,
      title: "Certified Coach",
      description:
        "Professional training in life coaching and emotional intelligence",
    },
  ];

  return (
    <section id="about-nisso" className="section-padding bg-white">
      <div className="container-custom">
        <div className="grid lg:grid-cols-2 gap-16 items-center">
          {/* Photo and Quote */}
          <div className="text-center lg:text-left">
            <div className="relative mb-8">
              {/* Photo placeholder */}
              <div className="w-80 h-80 mx-auto lg:mx-0 rounded-2xl bg-gradient-to-br from-blue-100 to-blue-200 flex items-center justify-center shadow-xl">
                <span className="text-6xl font-bold text-blue-600">NB</span>
              </div>
              {/* Decorative element */}
              <div className="absolute -bottom-4 -right-4 w-24 h-24 bg-blue-600 rounded-full opacity-10"></div>
            </div>

            <blockquote className="text-2xl font-medium text-gray-700 italic mb-6">
              &ldquo;Curiosity is my superpower. Every person I meet teaches me
              something new about the human experience.&rdquo;
            </blockquote>
            <cite className="text-blue-600 font-semibold text-lg">
              — Nisso Barokas
            </cite>
          </div>

          {/* Content */}
          <div>
            <h2 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
              Meet Nisso Barokas
            </h2>

            <div className="prose prose-lg text-gray-700 mb-8">
              <p className="mb-6">
                Nisso brings a unique blend of military discipline, business
                acumen, and deep emotional intelligence to his coaching
                practice. As a former special forces member and successful
                business leader, he understands both the strategic and human
                sides of transformation.
              </p>

              <p className="mb-6">
                But what truly sets Nisso apart is his genuine curiosity about
                people and his belief that humor and humanity are essential
                ingredients in any meaningful change. As a father and
                grandfather, he brings generational wisdom and a deep
                understanding of family dynamics to his work.
              </p>

              <p>
                Whether you&apos;re facing a career transition, relationship
                challenges, or simply seeking greater clarity and purpose, Nisso
                meets you where you are with empathy, practical tools, and the
                occasional well-timed joke.
              </p>
            </div>

            {/* Credentials Grid */}
            <div className="grid sm:grid-cols-2 gap-4 mb-8">
              {credentials.map((credential, index) => (
                <div
                  key={index}
                  className="flex items-start gap-3 p-4 bg-gray-50 rounded-lg"
                >
                  <div className="flex-shrink-0 mt-1">{credential.icon}</div>
                  <div>
                    <h4 className="font-semibold text-gray-900 mb-1">
                      {credential.title}
                    </h4>
                    <p className="text-sm text-gray-600">
                      {credential.description}
                    </p>
                  </div>
                </div>
              ))}
            </div>

            {/* Personal Philosophy */}
            <div className="bg-blue-50 rounded-xl p-6">
              <h3 className="text-xl font-semibold text-gray-900 mb-4">
                Nisso&apos;s Core Beliefs
              </h3>
              <ul className="space-y-2 text-gray-700">
                <li className="flex items-start gap-2">
                  <span className="text-blue-600 font-bold">•</span>
                  Every failure is a gift—you just have to open the box
                </li>
                <li className="flex items-start gap-2">
                  <span className="text-blue-600 font-bold">•</span>
                  Curiosity is more powerful than judgment
                </li>
                <li className="flex items-start gap-2">
                  <span className="text-blue-600 font-bold">•</span>
                  Emotional intelligence can be learned and practiced
                </li>
                <li className="flex items-start gap-2">
                  <span className="text-blue-600 font-bold">•</span>
                  Humor and humanity make everything better
                </li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default AboutNisso;
