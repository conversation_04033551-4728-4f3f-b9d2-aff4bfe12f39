'use client';

import { useState } from 'react';
import { ChevronDown, ChevronUp } from 'lucide-react';

const FAQ = () => {
  const [openIndex, setOpenIndex] = useState<number | null>(0);

  const faqs = [
    {
      question: "What's the difference between therapy and life coaching?",
      answer: "While therapy often focuses on healing past wounds and addressing mental health conditions, life coaching is forward-focused on achieving goals and creating positive change. Therapy asks 'Why?' while coaching asks 'What now?' and 'How?' As a life coach, I help you identify what you want, create actionable plans, and develop the skills to get there. If you're dealing with serious mental health issues, I'll recommend working with a licensed therapist alongside or instead of coaching."
    },
    {
      question: "How long does coaching typically take?",
      answer: "Every person and situation is unique, but most clients see significant progress within 3-6 months of regular sessions. Some people achieve their goals in a few months, while others prefer ongoing support for continued growth. During our discovery call, we'll discuss your specific goals and create a timeline that makes sense for your situation. The beauty of coaching is that you're in control of the pace and duration."
    },
    {
      question: "Do you offer online sessions?",
      answer: "Absolutely! I offer both in-person and online sessions via secure video conferencing. Many of my clients actually prefer online sessions for the convenience and comfort of being in their own space. The coaching experience is just as effective virtually, and it allows me to work with clients regardless of their location. We'll determine what works best for you during our initial conversation."
    },
    {
      question: "What if I'm not sure coaching is right for me?",
      answer: "That's exactly why I offer a free 30-minute discovery call! This gives us both a chance to explore your situation, discuss your goals, and see if we're a good fit. There's no pressure or obligation. Coaching works best when there's a strong connection and mutual trust, so it's important that you feel comfortable and confident in the process. If coaching isn't the right fit, I'm happy to suggest other resources that might better serve your needs."
    },
    {
      question: "How much does coaching cost?",
      answer: "Coaching packages are customized based on your specific needs, goals, and the level of support you're looking for. During our discovery call, we'll discuss your situation and I'll recommend the best approach and investment for your circumstances. I believe in making coaching accessible and will work with you to find a solution that fits your budget while providing the support you need to succeed."
    },
    {
      question: "What makes your approach different from other coaches?",
      answer: "My unique background combines military leadership, business experience, and deep emotional intelligence training. I bring real-world experience from leading teams under pressure, building successful businesses, and navigating my own life transitions. But what really sets me apart is my belief that humor and humanity are essential ingredients in transformation. I don't just give advice—I walk alongside you with empathy, practical tools, and yes, the occasional well-timed joke."
    },
    {
      question: "Do you work with couples and families?",
      answer: "Yes! I work with individuals, couples, and families. Relationship coaching focuses on improving communication, resolving conflicts, and building stronger emotional connections. Family coaching helps navigate dynamics between parents and children, siblings, or extended family members. Each situation is unique, and I tailor my approach to address the specific challenges and goals of your relationship or family system."
    },
    {
      question: "What happens between sessions?",
      answer: "Coaching doesn't stop when our session ends! You'll typically receive exercises, tools, or 'homework' to practice between sessions. I'm also available via email for quick questions or support. The real transformation happens in your daily life as you apply what we discuss. I provide resources, check-ins, and accountability to help you stay on track and make consistent progress toward your goals."
    }
  ];

  const toggleFAQ = (index: number) => {
    setOpenIndex(openIndex === index ? null : index);
  };

  return (
    <section id="faq" className="section-padding bg-white">
      <div className="container-custom">
        <div className="text-center mb-16">
          <h2 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
            Frequently Asked Questions
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
            Get answers to common questions about life coaching, the process, 
            and what you can expect when working with MissionMe.
          </p>
        </div>

        <div className="max-w-4xl mx-auto">
          <div className="space-y-4">
            {faqs.map((faq, index) => (
              <div
                key={index}
                className="bg-gray-50 rounded-xl overflow-hidden shadow-sm hover:shadow-md transition-shadow duration-300"
              >
                <button
                  onClick={() => toggleFAQ(index)}
                  className="w-full px-6 py-6 text-left flex items-center justify-between hover:bg-gray-100 transition-colors duration-200"
                >
                  <h3 className="text-lg font-semibold text-gray-900 pr-4">
                    {faq.question}
                  </h3>
                  <div className="flex-shrink-0">
                    {openIndex === index ? (
                      <ChevronUp className="w-5 h-5 text-blue-600" />
                    ) : (
                      <ChevronDown className="w-5 h-5 text-gray-400" />
                    )}
                  </div>
                </button>
                
                {openIndex === index && (
                  <div className="px-6 pb-6">
                    <div className="pt-2 border-t border-gray-200">
                      <p className="text-gray-700 leading-relaxed">
                        {faq.answer}
                      </p>
                    </div>
                  </div>
                )}
              </div>
            ))}
          </div>
        </div>

        {/* Still Have Questions CTA */}
        <div className="text-center mt-16">
          <div className="bg-blue-50 rounded-2xl p-8 md:p-12 max-w-3xl mx-auto">
            <h3 className="text-2xl font-bold text-gray-900 mb-4">
              Still Have Questions?
            </h3>
            <p className="text-gray-700 mb-6 leading-relaxed">
              Every situation is unique, and I'm here to address any specific concerns 
              or questions you might have about coaching and how it can help you.
            </p>
            <a
              href="#contact"
              className="btn-primary"
            >
              Schedule a Free Discovery Call
            </a>
          </div>
        </div>
      </div>
    </section>
  );
};

export default FAQ;
