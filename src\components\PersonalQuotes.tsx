"use client";

import { useState, useEffect } from "react";
import { Quote } from "lucide-react";

const PersonalQuotes = () => {
  const quotes = [
    {
      text: "Every failure is a gift—you just have to open the box.",
      context: "On reframing setbacks as learning opportunities",
    },
    {
      text: "Curiosity is my superpower.",
      context: "On the power of asking questions instead of making assumptions",
    },
    {
      text: "If you&apos;re reading this, you&apos;re already in the top 1%.",
      context:
        "On recognizing the privilege of seeking growth and self-improvement",
    },
    {
      text: "Emotional intelligence isn&apos;t a nice-to-have—it&apos;s your competitive advantage.",
      context: "On the importance of developing emotional skills",
    },
    {
      text: "The best leaders are the ones who make other people feel like leaders.",
      context: "On authentic leadership and empowerment",
    },
    {
      text: "Change happens in the space between comfort and panic.",
      context: "On finding the optimal zone for growth and transformation",
    },
    {
      text: "Your story isn&apos;t over—you&apos;re just turning the page.",
      context: "On resilience and the possibility of new beginnings",
    },
    {
      text: "Humor isn&apos;t the opposite of seriousness—it&apos;s the antidote to despair.",
      context: "On the healing power of laughter and lightness",
    },
  ];

  const [currentQuote, setCurrentQuote] = useState(0);

  useEffect(() => {
    const interval = setInterval(() => {
      setCurrentQuote((prev) => (prev + 1) % quotes.length);
    }, 5000);

    return () => clearInterval(interval);
  }, [quotes.length]);

  return (
    <section className="section-padding bg-gradient-to-br from-blue-600 to-blue-800 text-white">
      <div className="container-custom">
        <div className="text-center mb-16">
          <h2 className="text-4xl md:text-5xl font-bold mb-6">
            Words to Live By
          </h2>
          <p className="text-xl opacity-90 max-w-3xl mx-auto leading-relaxed">
            Insights and wisdom from years of coaching, leading, and learning.
            These are the principles that guide our work together.
          </p>
        </div>

        {/* Featured Quote */}
        <div className="max-w-4xl mx-auto text-center mb-16">
          <div className="relative">
            <Quote className="w-16 h-16 text-blue-300 mx-auto mb-6 opacity-50" />
            <blockquote className="text-3xl md:text-4xl lg:text-5xl font-bold mb-6 leading-tight">
              &ldquo;{quotes[currentQuote].text}&rdquo;
            </blockquote>
            <p className="text-lg md:text-xl opacity-80 italic">
              {quotes[currentQuote].context}
            </p>
          </div>
        </div>

        {/* Quote Navigation Dots */}
        <div className="flex justify-center space-x-2 mb-16">
          {quotes.map((_, index) => (
            <button
              key={index}
              onClick={() => setCurrentQuote(index)}
              className={`w-3 h-3 rounded-full transition-all duration-300 ${
                index === currentQuote
                  ? "bg-white w-8"
                  : "bg-blue-300 hover:bg-blue-200"
              }`}
            />
          ))}
        </div>

        {/* All Quotes Grid */}
        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
          {quotes.map((quote, index) => (
            <div
              key={index}
              className={`bg-white/10 backdrop-blur-sm rounded-xl p-6 transition-all duration-300 hover:bg-white/20 cursor-pointer ${
                index === currentQuote ? "ring-2 ring-white/50" : ""
              }`}
              onClick={() => setCurrentQuote(index)}
            >
              <blockquote className="text-lg font-medium mb-3">
                &ldquo;{quote.text}&rdquo;
              </blockquote>
              <p className="text-sm opacity-75 italic">{quote.context}</p>
            </div>
          ))}
        </div>

        {/* Bottom CTA */}
        <div className="text-center mt-16">
          <div className="bg-white/10 backdrop-blur-sm rounded-2xl p-8 md:p-12 max-w-3xl mx-auto">
            <h3 className="text-2xl font-bold mb-4">
              Ready to Write Your Next Chapter?
            </h3>
            <p className="text-lg opacity-90 mb-6 leading-relaxed">
              These principles come alive in our coaching conversations.
              Let&apos;s explore how they can guide your own journey of growth
              and transformation.
            </p>
            <a
              href="#contact"
              className="bg-white text-blue-600 hover:bg-gray-100 font-semibold py-3 px-8 rounded-lg transition-all duration-300 shadow-lg hover:shadow-xl transform hover:-translate-y-1 inline-block"
            >
              Start the Conversation
            </a>
          </div>
        </div>
      </div>
    </section>
  );
};

export default PersonalQuotes;
