import { User, Users, Heart, Compass } from 'lucide-react';

const Services = () => {
  const services = [
    {
      icon: <User className="w-12 h-12 text-blue-600" />,
      title: "Individual Coaching",
      subtitle: "Personal Growth & Life Transitions",
      description: "Navigate career changes, personal challenges, and major life decisions with clarity and confidence.",
      features: [
        "Career transition guidance",
        "Personal development planning",
        "Decision-making support",
        "Confidence building",
        "Goal setting and achievement"
      ],
      ideal: "Perfect for professionals, entrepreneurs, and anyone facing major life changes"
    },
    {
      icon: <Heart className="w-12 h-12 text-blue-600" />,
      title: "Couples & Family Coaching",
      subtitle: "Relationship Harmony & Communication",
      description: "Strengthen relationships, resolve conflicts, and build deeper emotional connections with those who matter most.",
      features: [
        "Communication skills training",
        "Conflict resolution strategies",
        "Emotional intelligence development",
        "Family dynamics improvement",
        "Relationship goal setting"
      ],
      ideal: "Ideal for couples and families seeking stronger, healthier relationships"
    },
    {
      icon: <Compass className="w-12 h-12 text-blue-600" />,
      title: "Young Adult Coaching",
      subtitle: "Direction & Emotional Strength",
      description: "Help young adults find their path, build emotional resilience, and develop the skills for lifelong success.",
      features: [
        "Life direction and purpose",
        "Emotional intelligence training",
        "Career exploration",
        "Relationship skills",
        "Stress management techniques"
      ],
      ideal: "Designed for young adults (20s-30s) seeking clarity and emotional growth"
    },
    {
      icon: <Users className="w-12 h-12 text-blue-600" />,
      title: "Group Workshops",
      subtitle: "Collective Growth & Learning",
      description: "Join like-minded individuals in focused workshops on emotional intelligence, leadership, and personal development.",
      features: [
        "Emotional intelligence workshops",
        "Leadership development",
        "Communication masterclasses",
        "Goal-setting intensives",
        "Peer learning and support"
      ],
      ideal: "Great for teams, organizations, and individuals who learn well in groups"
    }
  ];

  return (
    <section id="services" className="section-padding bg-white">
      <div className="container-custom">
        <div className="text-center mb-16">
          <h2 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
            Coaching Services
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
            Whether you're navigating life solo, strengthening relationships, or building 
            emotional intelligence, we have a coaching approach tailored to your needs.
          </p>
        </div>

        <div className="grid lg:grid-cols-2 gap-8 mb-16">
          {services.map((service, index) => (
            <div
              key={index}
              className="bg-gray-50 rounded-2xl p-8 card-hover"
            >
              {/* Header */}
              <div className="flex items-start gap-4 mb-6">
                <div className="flex-shrink-0">
                  {service.icon}
                </div>
                <div>
                  <h3 className="text-2xl font-bold text-gray-900 mb-2">
                    {service.title}
                  </h3>
                  <p className="text-lg font-medium text-blue-600">
                    {service.subtitle}
                  </p>
                </div>
              </div>

              {/* Description */}
              <p className="text-gray-700 mb-6 leading-relaxed">
                {service.description}
              </p>

              {/* Features */}
              <div className="mb-6">
                <h4 className="font-semibold text-gray-900 mb-3">What's Included:</h4>
                <ul className="space-y-2">
                  {service.features.map((feature, featureIndex) => (
                    <li key={featureIndex} className="flex items-start gap-2 text-gray-700">
                      <span className="text-blue-600 font-bold mt-1">•</span>
                      {feature}
                    </li>
                  ))}
                </ul>
              </div>

              {/* Ideal For */}
              <div className="bg-blue-50 rounded-lg p-4">
                <p className="text-sm font-medium text-blue-800">
                  <span className="font-semibold">Ideal for:</span> {service.ideal}
                </p>
              </div>
            </div>
          ))}
        </div>

        {/* Pricing and Process */}
        <div className="bg-gradient-to-br from-blue-50 to-blue-100 rounded-2xl p-8 md:p-12">
          <div className="grid md:grid-cols-2 gap-12">
            <div>
              <h3 className="text-3xl font-bold text-gray-900 mb-6">
                How It Works
              </h3>
              <div className="space-y-4">
                <div className="flex gap-4">
                  <div className="w-8 h-8 bg-blue-600 text-white rounded-full flex items-center justify-center font-bold text-sm flex-shrink-0">
                    1
                  </div>
                  <div>
                    <h4 className="font-semibold text-gray-900">Discovery Call</h4>
                    <p className="text-gray-700">Free 30-minute conversation to understand your goals and see if we're a good fit.</p>
                  </div>
                </div>
                <div className="flex gap-4">
                  <div className="w-8 h-8 bg-blue-600 text-white rounded-full flex items-center justify-center font-bold text-sm flex-shrink-0">
                    2
                  </div>
                  <div>
                    <h4 className="font-semibold text-gray-900">Assessment & Planning</h4>
                    <p className="text-gray-700">Deep dive into your current situation and create a personalized coaching plan.</p>
                  </div>
                </div>
                <div className="flex gap-4">
                  <div className="w-8 h-8 bg-blue-600 text-white rounded-full flex items-center justify-center font-bold text-sm flex-shrink-0">
                    3
                  </div>
                  <div>
                    <h4 className="font-semibold text-gray-900">Regular Sessions</h4>
                    <p className="text-gray-700">Weekly or bi-weekly sessions with homework, tools, and ongoing support.</p>
                  </div>
                </div>
                <div className="flex gap-4">
                  <div className="w-8 h-8 bg-blue-600 text-white rounded-full flex items-center justify-center font-bold text-sm flex-shrink-0">
                    4
                  </div>
                  <div>
                    <h4 className="font-semibold text-gray-900">Integration & Growth</h4>
                    <p className="text-gray-700">Apply new skills, track progress, and celebrate your transformation.</p>
                  </div>
                </div>
              </div>
            </div>
            
            <div>
              <h3 className="text-3xl font-bold text-gray-900 mb-6">
                Investment in Yourself
              </h3>
              <div className="bg-white rounded-xl p-6 shadow-lg">
                <p className="text-gray-700 mb-4">
                  Coaching packages are customized based on your specific needs and goals. 
                  During your discovery call, we'll discuss the best approach and investment 
                  for your situation.
                </p>
                <p className="text-gray-700 mb-6">
                  <strong>All packages include:</strong> Regular sessions, email support, 
                  practical tools and exercises, and a commitment to your success.
                </p>
                <a
                  href="#contact"
                  className="btn-primary w-full text-center"
                >
                  Schedule Discovery Call
                </a>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default Services;
