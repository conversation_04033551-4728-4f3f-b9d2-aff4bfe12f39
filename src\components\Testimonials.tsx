'use client';

import { useState } from 'react';
import { ChevronLeft, ChevronRight, Star } from 'lucide-react';

const Testimonials = () => {
  const testimonials = [
    {
      name: "<PERSON>",
      role: "Marketing Executive",
      content: "Working with <PERSON><PERSON> completely transformed how I approach challenges at work and in my personal life. His blend of practical wisdom and genuine humor made even the toughest conversations feel manageable. I finally have the confidence to pursue the career change I've been dreaming about.",
      rating: 5,
      highlight: "Transformed my approach to challenges"
    },
    {
      name: "<PERSON>",
      role: "Married Couple",
      content: "After 15 years of marriage, we thought we knew how to communicate. <PERSON><PERSON> showed us we were just scratching the surface. The tools he gave us for emotional intelligence and conflict resolution have brought us closer than we've ever been. Our kids notice the difference too.",
      rating: 5,
      highlight: "Brought us closer than ever"
    },
    {
      name: "<PERSON>",
      role: "Recent Graduate",
      content: "As a 25-year-old feeling completely lost after college, <PERSON><PERSON> helped me find direction and purpose. He didn't just tell me what to do—he helped me discover what I actually wanted. Six months later, I'm in a job I love and have the emotional tools to handle whatever comes next.",
      rating: 5,
      highlight: "Helped me find direction and purpose"
    },
    {
      name: "<PERSON>",
      role: "Small Business Owner",
      content: "<PERSON><PERSON>'s background in business and leadership was exactly what I needed. He understood the pressures I face as an entrepreneur while helping me develop the emotional intelligence to lead my team better. My business is thriving, and more importantly, I'm enjoying the journey.",
      rating: 5,
      highlight: "Business thriving and enjoying the journey"
    },
    {
      name: "The <PERSON> Family",
      role: "Family of Four",
      content: "Our teenage kids were struggling, and family dinners had become battlefields. Nisso helped us rebuild communication and understanding. He taught us that humor and empathy could coexist with boundaries and expectations. Our home feels peaceful again.",
      rating: 5,
      highlight: "Our home feels peaceful again"
    }
  ];

  const [currentIndex, setCurrentIndex] = useState(0);

  const nextTestimonial = () => {
    setCurrentIndex((prev) => (prev + 1) % testimonials.length);
  };

  const prevTestimonial = () => {
    setCurrentIndex((prev) => (prev - 1 + testimonials.length) % testimonials.length);
  };

  return (
    <section id="testimonials" className="section-padding bg-gray-50">
      <div className="container-custom">
        <div className="text-center mb-16">
          <h2 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
            Client Success Stories
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
            Real transformations from real people. These are the stories that fuel our passion 
            for helping others discover their potential and create meaningful change.
          </p>
        </div>

        {/* Carousel */}
        <div className="relative max-w-4xl mx-auto">
          <div className="bg-white rounded-2xl shadow-xl p-8 md:p-12">
            {/* Stars */}
            <div className="flex justify-center mb-6">
              {[...Array(testimonials[currentIndex].rating)].map((_, i) => (
                <Star key={i} className="w-6 h-6 text-yellow-400 fill-current" />
              ))}
            </div>

            {/* Testimonial Content */}
            <blockquote className="text-xl md:text-2xl text-gray-700 italic text-center mb-8 leading-relaxed">
              "{testimonials[currentIndex].content}"
            </blockquote>

            {/* Highlight */}
            <div className="text-center mb-6">
              <span className="inline-block bg-blue-100 text-blue-800 px-4 py-2 rounded-full text-sm font-medium">
                {testimonials[currentIndex].highlight}
              </span>
            </div>

            {/* Author */}
            <div className="text-center">
              <cite className="text-lg font-semibold text-gray-900 not-italic">
                {testimonials[currentIndex].name}
              </cite>
              <p className="text-blue-600 font-medium">
                {testimonials[currentIndex].role}
              </p>
            </div>
          </div>

          {/* Navigation Buttons */}
          <button
            onClick={prevTestimonial}
            className="absolute left-0 top-1/2 transform -translate-y-1/2 -translate-x-4 w-12 h-12 bg-white rounded-full shadow-lg flex items-center justify-center text-gray-600 hover:text-blue-600 hover:shadow-xl transition-all duration-300"
          >
            <ChevronLeft size={24} />
          </button>
          <button
            onClick={nextTestimonial}
            className="absolute right-0 top-1/2 transform -translate-y-1/2 translate-x-4 w-12 h-12 bg-white rounded-full shadow-lg flex items-center justify-center text-gray-600 hover:text-blue-600 hover:shadow-xl transition-all duration-300"
          >
            <ChevronRight size={24} />
          </button>

          {/* Dots Indicator */}
          <div className="flex justify-center mt-8 space-x-2">
            {testimonials.map((_, index) => (
              <button
                key={index}
                onClick={() => setCurrentIndex(index)}
                className={`w-3 h-3 rounded-full transition-all duration-300 ${
                  index === currentIndex
                    ? 'bg-blue-600 w-8'
                    : 'bg-gray-300 hover:bg-gray-400'
                }`}
              />
            ))}
          </div>
        </div>

        {/* Stats */}
        <div className="grid md:grid-cols-3 gap-8 mt-16">
          <div className="text-center">
            <div className="text-4xl font-bold text-blue-600 mb-2">95%</div>
            <p className="text-gray-600">Client Satisfaction Rate</p>
          </div>
          <div className="text-center">
            <div className="text-4xl font-bold text-blue-600 mb-2">200+</div>
            <p className="text-gray-600">Lives Transformed</p>
          </div>
          <div className="text-center">
            <div className="text-4xl font-bold text-blue-600 mb-2">10+</div>
            <p className="text-gray-600">Years of Experience</p>
          </div>
        </div>
      </div>
    </section>
  );
};

export default Testimonials;
