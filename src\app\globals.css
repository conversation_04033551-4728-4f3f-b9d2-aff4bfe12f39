@import "tailwindcss";

:root {
  --background: #ffffff;
  --foreground: #171717;

  /* MissionMe color palette inspired by water/swimming theme */
  --ocean-blue: #0077be;
  --deep-blue: #003f5c;
  --light-blue: #4a90e2;
  --aqua: #7dd3fc;
  --seafoam: #a7f3d0;
  --sand: #fef3c7;
  --coral: #fb7185;
  --pearl: #f8fafc;
  --charcoal: #374151;
  --slate: #64748b;
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-inter);
  --font-serif: var(--font-playfair);

  /* Custom colors */
  --color-ocean: var(--ocean-blue);
  --color-deep: var(--deep-blue);
  --color-light: var(--light-blue);
  --color-aqua: var(--aqua);
  --color-seafoam: var(--seafoam);
  --color-sand: var(--sand);
  --color-coral: var(--coral);
  --color-pearl: var(--pearl);
  --color-charcoal: var(--charcoal);
  --color-slate: var(--slate);
}

@media (prefers-color-scheme: dark) {
  :root {
    --background: #0a0a0a;
    --foreground: #ededed;
  }
}

html {
  scroll-behavior: smooth;
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: var(--font-inter), system-ui, sans-serif;
  line-height: 1.6;
}

h1, h2, h3, h4, h5, h6 {
  font-family: var(--font-playfair), serif;
  font-weight: 600;
}

/* Custom utility classes */
.btn-primary {
  @apply bg-blue-600 hover:bg-blue-700 text-white font-semibold py-3 px-6 rounded-lg transition-all duration-300 shadow-lg hover:shadow-xl transform hover:-translate-y-1;
}

.btn-secondary {
  @apply bg-white hover:bg-gray-50 text-blue-600 font-semibold py-3 px-6 rounded-lg border-2 border-blue-600 transition-all duration-300 shadow-lg hover:shadow-xl transform hover:-translate-y-1;
}

.section-padding {
  @apply py-16 md:py-24;
}

.container-custom {
  @apply max-w-7xl mx-auto px-4 sm:px-6 lg:px-8;
}

.text-gradient {
  @apply bg-gradient-to-r from-blue-600 to-blue-800 bg-clip-text text-transparent;
}

.card-hover {
  @apply transition-all duration-300 hover:shadow-xl hover:-translate-y-2;
}

.bg-ocean {
  background: linear-gradient(135deg, #0077be 0%, #003f5c 100%);
}

.bg-wave {
  background: linear-gradient(135deg, #7dd3fc 0%, #4a90e2 50%, #0077be 100%);
}
